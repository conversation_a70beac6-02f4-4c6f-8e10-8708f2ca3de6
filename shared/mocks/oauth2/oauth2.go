package oauth2

import (
	"context"
	"net/http"
	"net/url"
	"time"

	"github.com/coreos/go-oidc"
	"golang.org/x/oauth2"
)

// OAuth2ConfigInterface defines the interface for OAuth2 configuration
// This interface matches the methods used by oauth2.Config from golang.org/x/oauth2
type OAuth2ConfigInterface interface {
	AuthCodeURL(state string, opts ...oauth2.AuthCodeOption) string
	Exchange(ctx context.Context, code string, opts ...oauth2.AuthCodeOption) (*oauth2.Token, error)
	Client(ctx context.Context, t *oauth2.Token) *http.Client
}

// OIDCProviderInterface defines the interface for OIDC provider
// This interface matches the methods used by oidc.Provider from github.com/coreos/go-oidc
type OIDCProviderInterface interface {
	Endpoint() oauth2.Endpoint
	Verifier(config *oidc.Config) *oidc.IDTokenVerifier
}

// IDTokenVerifierInterface defines the interface for ID token verification
// This interface matches the methods used by oidc.IDTokenVerifier
type IDTokenVerifierInterface interface {
	Verify(ctx context.Context, rawIDToken string) (*oidc.IDToken, error)
}

// IDTokenInterface defines the interface for ID tokens
// This interface matches the methods used by oidc.IDToken
type IDTokenInterface interface {
	Claims(v interface{}) error
}

// GitHubClientInterface defines the interface for GitHub API client
// Based on the oauth2_guide.txt example for mocking GitHub API calls
type GitHubClientInterface interface {
	NewClient(httpClient *http.Client) GitHubClient
}

// GitHubClient manages communication with the GitHub API
type GitHubClient struct {
	Repositories RepositoriesService
	Users        UsersService
}

// RepositoriesService handles communication with repository related methods
type RepositoriesService interface {
	Get(context.Context, string, string) (interface{}, interface{}, error)
	GetByID(context.Context, int64) (interface{}, interface{}, error)
}

// UsersService handles communication with user related methods
type UsersService interface {
	Get(context.Context, string) (interface{}, interface{}, error)
}

// =============================================================================
// MOCK IMPLEMENTATIONS
// =============================================================================

// FakeOAuth2Config is a mock implementation of OAuth2ConfigInterface
type FakeOAuth2Config struct {
	AuthCodeURLFunc func(state string, opts ...oauth2.AuthCodeOption) string
	ExchangeFunc    func(ctx context.Context, code string, opts ...oauth2.AuthCodeOption) (*oauth2.Token, error)
	ClientFunc      func(ctx context.Context, t *oauth2.Token) *http.Client
}

// AuthCodeURL redirects to our own server for testing
func (f *FakeOAuth2Config) AuthCodeURL(state string, opts ...oauth2.AuthCodeOption) string {
	if f.AuthCodeURLFunc != nil {
		return f.AuthCodeURLFunc(state, opts...)
	}

	// Default implementation - redirect to localhost for testing
	u := url.URL{
		Scheme: "http",
		Host:   "localhost:4200",
		Path:   "/login/oauth/authorize",
	}

	v := url.Values{}
	v.Set("state", state)
	u.RawQuery = v.Encode()

	return u.String()
}

// Exchange takes the code and returns a mock token
func (f *FakeOAuth2Config) Exchange(ctx context.Context, code string, opts ...oauth2.AuthCodeOption) (*oauth2.Token, error) {
	if f.ExchangeFunc != nil {
		return f.ExchangeFunc(ctx, code, opts...)
	}

	// Default implementation - return a valid mock token
	return &oauth2.Token{
		AccessToken:  "mock_access_token",
		TokenType:    "Bearer",
		RefreshToken: "mock_refresh_token",
		Expiry:       time.Now().Add(1 * time.Hour),
	}, nil
}

// Client returns a mock HTTP client
func (f *FakeOAuth2Config) Client(ctx context.Context, t *oauth2.Token) *http.Client {
	if f.ClientFunc != nil {
		return f.ClientFunc(ctx, t)
	}

	// Default implementation - return a standard HTTP client
	return &http.Client{
		Timeout: 30 * time.Second,
	}
}

// FakeOIDCProvider is a mock implementation of OIDCProviderInterface
type FakeOIDCProvider struct {
	EndpointFunc func() oauth2.Endpoint
	VerifierFunc func(config *oidc.Config) *oidc.IDTokenVerifier
}

// Endpoint returns a mock OAuth2 endpoint
func (f *FakeOIDCProvider) Endpoint() oauth2.Endpoint {
	if f.EndpointFunc != nil {
		return f.EndpointFunc()
	}

	// Default implementation - return mock endpoints
	return oauth2.Endpoint{
		AuthURL:  "http://localhost:8091/auth/realms/test/protocol/openid-connect/auth",
		TokenURL: "http://localhost:8091/auth/realms/test/protocol/openid-connect/token",
	}
}

// Verifier returns a mock ID token verifier
func (f *FakeOIDCProvider) Verifier(config *oidc.Config) *oidc.IDTokenVerifier {
	if f.VerifierFunc != nil {
		return f.VerifierFunc(config)
	}

	// Return nil for default - tests should provide their own verifier if needed
	return nil
}

// FakeIDTokenVerifier is a mock implementation of IDTokenVerifierInterface
type FakeIDTokenVerifier struct {
	VerifyFunc func(ctx context.Context, rawIDToken string) (*oidc.IDToken, error)
}

// Verify returns a mock ID token
func (f *FakeIDTokenVerifier) Verify(ctx context.Context, rawIDToken string) (*oidc.IDToken, error) {
	if f.VerifyFunc != nil {
		return f.VerifyFunc(ctx, rawIDToken)
	}

	// Return nil for default - tests should provide their own implementation
	return nil, nil
}

// FakeIDToken is a mock implementation of IDTokenInterface
type FakeIDToken struct {
	ClaimsFunc func(v interface{}) error
}

// Claims extracts claims from the mock ID token
func (f *FakeIDToken) Claims(v interface{}) error {
	if f.ClaimsFunc != nil {
		return f.ClaimsFunc(v)
	}

	// Default implementation - populate with mock claims
	if claims, ok := v.(*map[string]interface{}); ok {
		*claims = map[string]interface{}{
			"sub":   "mock_subject_123",
			"iss":   "http://localhost:8091/auth/realms/test",
			"email": "<EMAIL>",
			"name":  "Test User",
			"iat":   time.Now().Unix(),
			"exp":   time.Now().Add(time.Hour).Unix(),
		}
	}

	return nil
}

// =============================================================================
// GITHUB API MOCKS (Based on oauth2_guide.txt)
// =============================================================================

// FakeGitHubClient is a mock implementation of GitHubClientInterface
type FakeGitHubClient struct {
	NewClientFunc func(httpClient *http.Client) GitHubClient
}

// NewClient returns a mock GitHub client
func (f *FakeGitHubClient) NewClient(httpClient *http.Client) GitHubClient {
	if f.NewClientFunc != nil {
		return f.NewClientFunc(httpClient)
	}

	// Default implementation - return mock services
	return GitHubClient{
		Repositories: &FakeRepositoriesService{},
		Users:        &FakeUsersService{},
	}
}

// FakeRepositoriesService mocks GitHub repositories service
type FakeRepositoriesService struct {
	GetFunc     func(context.Context, string, string) (interface{}, interface{}, error)
	GetByIDFunc func(context.Context, int64) (interface{}, interface{}, error)
}

// Get returns a mock repository
func (f *FakeRepositoriesService) Get(ctx context.Context, owner, repo string) (interface{}, interface{}, error) {
	if f.GetFunc != nil {
		return f.GetFunc(ctx, owner, repo)
	}

	// Default implementation - return mock repository data
	mockRepo := map[string]interface{}{
		"id":               185409993,
		"name":             "test-repo",
		"description":      "A test repository",
		"language":         "Go",
		"stargazers_count": 42,
		"html_url":         "https://github.com/test/test-repo",
		"full_name":        "test/test-repo",
	}

	return mockRepo, nil, nil
}

// GetByID returns a mock repository by ID
func (f *FakeRepositoriesService) GetByID(ctx context.Context, id int64) (interface{}, interface{}, error) {
	if f.GetByIDFunc != nil {
		return f.GetByIDFunc(ctx, id)
	}

	// Default implementation - return mock repository data
	mockRepo := map[string]interface{}{
		"id":               id,
		"name":             "test-repo",
		"description":      "A test repository",
		"language":         "Go",
		"stargazers_count": 42,
		"html_url":         "https://github.com/test/test-repo",
		"full_name":        "test/test-repo",
	}

	return mockRepo, nil, nil
}

// FakeUsersService mocks GitHub users service
type FakeUsersService struct {
	GetFunc func(context.Context, string) (interface{}, interface{}, error)
}

// Get returns a mock user
func (f *FakeUsersService) Get(ctx context.Context, user string) (interface{}, interface{}, error) {
	if f.GetFunc != nil {
		return f.GetFunc(ctx, user)
	}

	// Default implementation - return mock user data
	mockUser := map[string]interface{}{
		"login":        "testuser",
		"id":           12345,
		"name":         "Test User",
		"email":        "<EMAIL>",
		"company":      "Test Company",
		"location":     "Test City",
		"bio":          "A test user",
		"public_repos": 10,
		"followers":    5,
		"following":    3,
	}

	return mockUser, nil, nil
}

// =============================================================================
// UTILITY FUNCTIONS AND CONSTRUCTORS
// =============================================================================

// NewFakeOAuth2Config creates a new FakeOAuth2Config with default behavior
func NewFakeOAuth2Config() *FakeOAuth2Config {
	return &FakeOAuth2Config{}
}

// NewFakeOAuth2ConfigWithToken creates a FakeOAuth2Config that returns a specific token
func NewFakeOAuth2ConfigWithToken(token *oauth2.Token) *FakeOAuth2Config {
	return &FakeOAuth2Config{
		ExchangeFunc: func(ctx context.Context, code string, opts ...oauth2.AuthCodeOption) (*oauth2.Token, error) {
			return token, nil
		},
	}
}

// NewFakeOAuth2ConfigWithError creates a FakeOAuth2Config that returns an error on Exchange
func NewFakeOAuth2ConfigWithError(err error) *FakeOAuth2Config {
	return &FakeOAuth2Config{
		ExchangeFunc: func(ctx context.Context, code string, opts ...oauth2.AuthCodeOption) (*oauth2.Token, error) {
			return nil, err
		},
	}
}

// NewFakeOIDCProvider creates a new FakeOIDCProvider with default behavior
func NewFakeOIDCProvider() *FakeOIDCProvider {
	return &FakeOIDCProvider{}
}

// NewFakeIDTokenVerifier creates a new FakeIDTokenVerifier with default behavior
func NewFakeIDTokenVerifier() *FakeIDTokenVerifier {
	return &FakeIDTokenVerifier{}
}

// NewFakeIDToken creates a new FakeIDToken with default claims
func NewFakeIDToken() *FakeIDToken {
	return &FakeIDToken{}
}

// NewFakeIDTokenWithClaims creates a FakeIDToken that returns specific claims
func NewFakeIDTokenWithClaims(claims map[string]interface{}) *FakeIDToken {
	return &FakeIDToken{
		ClaimsFunc: func(v interface{}) error {
			if claimsPtr, ok := v.(*map[string]interface{}); ok {
				*claimsPtr = claims
			}
			return nil
		},
	}
}

// NewFakeGitHubClient creates a new FakeGitHubClient with default behavior
func NewFakeGitHubClient() *FakeGitHubClient {
	return &FakeGitHubClient{}
}

// CreateMockToken creates a mock OAuth2 token with default values
func CreateMockToken() *oauth2.Token {
	return &oauth2.Token{
		AccessToken:  "mock_access_token",
		TokenType:    "Bearer",
		RefreshToken: "mock_refresh_token",
		Expiry:       time.Now().Add(1 * time.Hour),
	}
}

// CreateMockTokenWithIDToken creates a mock OAuth2 token with an ID token
func CreateMockTokenWithIDToken(idToken string) *oauth2.Token {
	token := CreateMockToken()
	// Add ID token as extra parameter (common in OIDC flows)
	token = token.WithExtra(map[string]interface{}{
		"id_token": idToken,
	})
	return token
}

// CreateMockClaims creates mock OIDC claims for testing
func CreateMockClaims() map[string]interface{} {
	return map[string]interface{}{
		"sub":   "mock_subject_123",
		"iss":   "http://localhost:8091/auth/realms/test",
		"email": "<EMAIL>",
		"name":  "Test User",
		"iat":   time.Now().Unix(),
		"exp":   time.Now().Add(time.Hour).Unix(),
	}
}
